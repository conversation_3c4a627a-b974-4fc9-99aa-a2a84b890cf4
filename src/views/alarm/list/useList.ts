import { ref, reactive } from 'vue'
import { getAlarmDetailsPage, deleteAlarmDetails } from '@/api/alg/alarm-details'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { AlarmDetails, QueryParams } from './types'

export function useList() {
  const loading = ref(true)
  const alarmDetailsList = ref<AlarmDetails[]>([])
  const total = ref(0)

  const queryParams = reactive<QueryParams>({
    pageNo: 1,
    pageSize: 10,
    taskId: undefined,
    taskName: undefined,
    alarmTypeName: undefined,
    status: undefined,
    createTimeStart: undefined,
    createTimeEnd: undefined
  })

  /** 查询列表 */
  const getList = async () => {
    loading.value = true
    try {
      const data = await getAlarmDetailsPage(queryParams)
      alarmDetailsList.value = data.list
      total.value = data.total
    } catch (error) {
      ElMessage.error('获取告警明细列表失败')
      console.error('获取告警明细列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 处理告警操作 */
  const handleProcess = (row: AlarmDetails, dialogRef?: any) => {
    if (dialogRef) {
      dialogRef.open(row)
    } else {
      ElMessage.info('处理功能开发中...')
    }
  }

  /** 清除告警操作 */
  const handleClear = async (row: AlarmDetails) => {
    try {
      await ElMessageBox.confirm(`确认清除告警"${row.alarmTypeName}"?`, '提示', {
        type: 'warning',
        confirmButtonText: '确认清除',
        cancelButtonText: '取消'
      })
      await deleteAlarmDetails({ id: row.id })
      ElMessage.success('清除成功')
      await getList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('清除失败')
        console.error('清除告警失败:', error)
      }
    }
  }

  return {
    loading,
    alarmDetailsList,
    total,
    queryParams,
    getList,
    handleQuery,
    handleProcess,
    handleClear
  }
}
