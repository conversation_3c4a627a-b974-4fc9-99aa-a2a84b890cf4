<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container mb-3">
      <Filter :query-params="queryParams" @query="handleQuery" @reset="resetQuery" />
    </div>

    <!-- 数据列表 -->
    <div class="data-list-container">
      <div class="card-header-container">
        <h5 class="card-title">告警列表</h5>
      </div>

      <!-- 表格 -->
      <div class="table-wrapper">
        <el-table
          :data="alarmDetailsList"
          v-loading="loading"
          style="width: 100%"
          :header-cell-style="{ backgroundColor: '#f2f3f5' }"
        >
          <el-table-column prop="taskName" label="任务名称" show-overflow-tooltip />
          <el-table-column prop="alarmTypeName" label="告警类型" show-overflow-tooltip />
          <el-table-column prop="status" label="告警状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getAlarmStatusColor(row.status)" size="small">
                {{ getAlarmStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="alarmTime" label="告警时间" width="180">
            <template #default="{ row }">
              <span>{{ row.alarmTime ? formatDate(row.alarmTime) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="alarmFileUrl" label="告警照片" width="100" align="center">
            <template #default="{ row }">
              <el-button
                v-if="row.alarmFileUrl"
                link
                type="primary"
                @click="previewImage(row.alarmFileUrl)"
              >
                查看照片
              </el-button>
              <span v-else class="text-gray-400 table-text">无照片</span>
            </template>
          </el-table-column>
          <el-table-column prop="alarmNoticeUserNames" label="通知人员" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="table-text" style="color: #409eff">{{
                row.alarmNoticeUserNames || '暂无'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180">
            <template #default="{ row }">
              <span class="table-text">{{
                row.createTime ? formatDate(row.createTime) : '-'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="processResult" label="处理结果" width="100">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleProcess(row, alarmDetailDialogRef)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button link type="danger" @click="handleClear(row)">清除</el-button>
              <el-button link type="primary" @click="handleProcess(row, alarmDetailDialogRef)"
                >处理</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="PAGE_SIZES as number[]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>

    <!-- 告警详情弹窗 -->
    <AlarmDetailDialog ref="alarmDetailDialogRef" @success="handleDetailSuccess" />

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImageViewer"
      :url-list="[previewImageUrl]"
      @close="closeImageViewer"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Filter from './components/Filter.vue'
import AlarmDetailDialog from './components/AlarmDetailDialog.vue'
import { useList } from './useList'
import { PAGE_SIZES } from './constants'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'AlarmDetails' })

const alarmDetailDialogRef = ref()
const showImageViewer = ref(false)
const previewImageUrl = ref('')

const {
  loading,
  total,
  alarmDetailsList,
  queryParams,
  getList,
  handleQuery,
  handleProcess,
  handleClear
} = useList()

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.taskName = undefined
  queryParams.alarmTypeName = undefined
  queryParams.status = undefined
  queryParams.createTimeStart = undefined
  queryParams.createTimeEnd = undefined
  handleQuery()
}

/** 获取告警状态标签 */
const getAlarmStatusLabel = (status: number) => {
  const statusMap = {
    0: '待处理',
    1: '处理中',
    2: '已处理',
    3: '已忽略'
  }
  return statusMap[status] || '未知'
}

/** 获取告警状态颜色 */
const getAlarmStatusColor = (status: number) => {
  const colorMap = {
    0: 'warning',
    1: 'primary',
    2: 'success',
    3: 'info'
  }
  return colorMap[status] || 'info'
}

/** 详情弹窗成功回调 */
const handleDetailSuccess = () => {
  getList()
}

/** 预览图片 */
const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl
  showImageViewer.value = true
}

/** 关闭图片预览 */
const closeImageViewer = () => {
  showImageViewer.value = false
  previewImageUrl.value = ''
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 76px);
  box-sizing: border-box;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.data-list-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  padding: 14px 20px;
  flex: 1;
  box-sizing: border-box;
}
.table-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-top: 14px;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
.filter-container {
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 0;
}
.card-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-text {
  font-size: 14px;
  line-height: 1.5;
}
</style>
