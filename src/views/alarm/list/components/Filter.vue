<template>
  <el-form :model="localParams" ref="queryFormRef" :inline="true">
    <el-form-item label="任务名称" prop="taskName">
      <el-input
        v-model="localParams.taskName"
        placeholder="请输入任务名称"
        clearable
        class="!w-240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="告警类型" prop="alarmTypeName">
      <el-select v-model="localParams.alarmTypeName" placeholder="全部" clearable class="!w-160px">
        <el-option label="安全帽识别" value="安全帽识别" />
        <el-option label="反光衣识别" value="反光衣识别" />
        <el-option label="烟火识别" value="烟火识别" />
        <el-option label="人员入侵" value="人员入侵" />
        <el-option label="车辆识别" value="车辆识别" />
      </el-select>
    </el-form-item>
    <el-form-item label="状态" prop="status">
      <el-select v-model="localParams.status" placeholder="全部" clearable class="!w-140px">
        <el-option label="待处理" :value="0" />
        <el-option label="处理中" :value="1" />
        <el-option label="已处理" :value="2" />
        <el-option label="已忽略" :value="3" />
      </el-select>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-date-picker
        :model-value="createTimeRange"
        @update:model-value="handleCreateTimeChange"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="!w-240px"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { QueryParams } from '../types'

const props = defineProps<{
  queryParams: QueryParams
}>()

const emit = defineEmits<{
  query: []
  reset: []
}>()

const queryFormRef = ref()
const localParams = reactive({ ...props.queryParams })

// 创建时间范围的计算属性
const createTimeRange = computed({
  get: () => {
    if (localParams.createTimeStart && localParams.createTimeEnd) {
      return [localParams.createTimeStart, localParams.createTimeEnd] as [string, string]
    }
    return undefined
  },
  set: (value: [string, string] | undefined) => {
    if (value && value.length === 2) {
      // 确保开始时间是当天的00:00:00，结束时间是当天的23:59:59
      const startDate = value[0].split(' ')[0] // 只取日期部分
      const endDate = value[1].split(' ')[0] // 只取日期部分
      localParams.createTimeStart = `${startDate} 00:00:00`
      localParams.createTimeEnd = `${endDate} 23:59:59`
    } else {
      localParams.createTimeStart = undefined
      localParams.createTimeEnd = undefined
    }
  }
})

// 处理时间范围变化
const handleCreateTimeChange = (value: [string, string] | undefined) => {
  createTimeRange.value = value
}

watch(
  () => props.queryParams,
  (newVal) => {
    Object.assign(localParams, newVal)
  },
  { deep: true }
)

const handleQuery = () => {
  Object.assign(props.queryParams, localParams)
  emit('query')
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  emit('reset')
}
</script>
