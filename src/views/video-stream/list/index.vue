<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container mb-3">
      <Filter :query-params="queryParams" @query="handleQuery" @reset="resetQuery" />
    </div>

    <!-- 数据列表 -->
    <div class="data-list-container">
      <div class="card-header-container">
        <h5 class="card-title">视频流列表</h5>
        <div class="header-actions">
          <el-button
            type="danger"
            :disabled="selectedIds.length === 0"
            @click="() => handleDeleteMultiple(selectedIds)"
          >
            删除
          </el-button>
          <el-button type="primary" @click="handleCreate">创建视频流</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <div class="table-wrapper">
        <el-table
          :data="deviceList"
          v-loading="loading"
          style="width: 100%"
          :header-cell-style="{ backgroundColor: '#f2f3f5' }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="deviceName" label="摄像头名称" min-width="120" />
          <el-table-column prop="liveUrl" label="URL" min-width="200" />
          <el-table-column prop="username" label="用户名" width="100" />
          <el-table-column prop="password" label="密码" width="100">
            <template #default="{ row }">
              <span>{{ row.password ? '***' : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleView(row)">查看</el-button>
              <el-button link type="primary" @click="handleUpdate(row)">编辑</el-button>
              <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="PAGE_SIZES as number[]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>

    <!-- 创建/编辑视频流弹窗 -->
    <CreateDialog ref="formDialogRef" @success="getList" />

    <!-- 视频流详情弹窗 -->
    <VideoStreamDialog ref="viewDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Filter from './components/Filter.vue'
import CreateDialog from './components/CreateDialog.vue'
import VideoStreamDialog from './components/VideoStreamDialog.vue'
import { useList } from './useList'
import { PAGE_SIZES } from './constants'
import type { ResourceDevice } from './types'

defineOptions({ name: 'VideoStreamList' })

const formDialogRef = ref()
const viewDialogRef = ref()

const {
  loading,
  total,
  deviceList,
  queryParams,
  getList,
  handleQuery,
  handleDelete,
  handleDeleteMultiple
} = useList()

const selectedIds = ref<number[]>([])

const handleSelectionChange = (selection: ResourceDevice[]) => {
  selectedIds.value = selection.map((item) => item.id)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.deviceName = undefined
  handleQuery()
}

/** 新建按钮操作 */
const handleCreate = () => {
  formDialogRef.value?.open()
}

/** 查看按钮操作 */
const handleView = (row: ResourceDevice) => {
  viewDialogRef.value?.open(row)
}

/** 编辑按钮操作 */
const handleUpdate = (row: ResourceDevice) => {
  formDialogRef.value?.open(row)
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 76px);
  box-sizing: border-box;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.data-list-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  padding: 14px 20px;
  flex: 1;
  box-sizing: border-box;
}
.table-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-top: 14px;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
.filter-container {
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 0;
}
.card-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-actions {
  display: flex;
}
</style>
