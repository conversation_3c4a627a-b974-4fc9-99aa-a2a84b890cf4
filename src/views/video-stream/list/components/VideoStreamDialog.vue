<template>
  <el-dialog
    v-model="dialogVisible"
    title="视频流详情"
    width="800px"
    :before-close="handleClose"
    append-to-body
    class="video-stream-dialog"
  >
    <div class="dialog-content">
      <!-- 视频预览区域 -->
      <div class="video-frame">
        <SimplePlayer
          v-if="deviceData.liveUrl"
          :src="deviceData.liveUrl"
          :volume="0.6"
          :controls="true"
          :autoplay="true"
          :loop="true"
          class="video-player"
        />
        <div v-else class="no-video">暂无视频流</div>
      </div>

      <!-- 设备信息区域 -->
      <div class="device-info-section">
        <h4 class="info-title">设备信息</h4>
        <div class="info-item">
          <span class="info-label">摄像头名称：</span>
          <span class="info-value">{{ deviceData.deviceName || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">直播流地址：</span>
          <span class="info-value">{{ deviceData.liveUrl || '-' }}</span>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SimplePlayer from '@/components/SimplePlayer/xgplayer.vue'
import type { ResourceDevice } from '../types'

defineOptions({ name: 'VideoStreamDialog' })

const dialogVisible = ref(false)

const deviceData = ref<Partial<ResourceDevice>>({})

/** 打开弹窗 */
const open = (data?: ResourceDevice) => {
  deviceData.value = data || {}
  dialogVisible.value = true
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  deviceData.value = {}
}

defineExpose({
  open
})
</script>

<style scoped>
/* 视频容器 */
.video-frame {
  position: relative;
  width: 100%;
  height: 480px; /* 增大视频高度 */
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.video-player {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.no-video {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  font-size: 16px;
  background-color: #f5f7fa;
}

/* 设备信息区域 */
.device-info-section {
  padding: 20px 0;
}

.info-title {
  font-size: 16px;
  font-weight: 500;
  color: #1f2329;
  margin: 0 0 16px 0;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.info-label {
  font-size: 14px;
  font-weight: 400;
  color: #646a73;
  width: 120px;
  flex-shrink: 0;
}

.info-value {
  font-size: 14px;
  font-weight: 400;
  color: #1f2329;
  flex: 1;
  word-break: break-all;
}

/* 底部按钮 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 20px 0;
}

:deep(.dialog-footer .el-button) {
  width: 88px;
  height: 34px;
  padding: 8px 15px;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 400;
  line-height: 16px;
}

:deep(.dialog-footer .el-button--default) {
  border: 1px solid #d8d8d8;
  background-color: #ffffff;
  color: #222529;
}

:deep(.dialog-footer .el-button--primary) {
  background-color: #0057d9;
  border: none;
  color: #ffffff;
}
</style>
