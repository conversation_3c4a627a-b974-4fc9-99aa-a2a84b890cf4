import { TASK_STATUS, TASK_TYPES } from './constants'

export type TaskStatus = (typeof TASK_STATUS)[keyof typeof TASK_STATUS]
export type TaskType = (typeof TASK_TYPES)[keyof typeof TASK_TYPES]

export interface Task {
  id: number
  name: string
  taskType: TaskType
  taskDataName: string
  alarmNoticeUserName: string
  status: TaskStatus
  createTime: string
}

export interface QueryParams {
  pageNo: number
  pageSize: number
  keyword?: string
  taskType?: TaskType
  status?: TaskStatus
  createTimeStart?: string
  createTimeEnd?: string
}
