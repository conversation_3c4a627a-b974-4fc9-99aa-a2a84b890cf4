<template>
  <div :id="playerId" class="xg-player-container"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import Player from 'xgplayer'
import HlsPlugin from 'xgplayer-hls'
import FlvPlugin from 'xgplayer-flv'
import 'xgplayer/dist/index.min.css'

defineOptions({ name: 'XgPlayer' })

interface Props {
  src: string
  width?: number | string
  height?: number | string
  autoplay?: boolean
  loop?: boolean
  volume?: number
  controls?: boolean
  poster?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  autoplay: false,
  loop: false,
  volume: 0.6,
  controls: true,
  poster: ''
})

const playerId = `xgplayer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
let player: any = null

// 检测视频格式
const getVideoType = (url: string) => {
  if (url.includes('.m3u8')) {
    return 'hls'
  } else if (url.includes('.flv')) {
    return 'flv'
  } else {
    return 'mp4'
  }
}

// 初始化播放器
const initPlayer = () => {
  if (!props.src) return

  const videoType = getVideoType(props.src)

  // 根据视频类型选择插件
  const plugins: any[] = []
  if (videoType === 'hls') {
    plugins.push(HlsPlugin)
  } else if (videoType === 'flv') {
    plugins.push(FlvPlugin)
  }

  // 播放器配置
  const config = {
    id: playerId,
    url: props.src,
    width: props.width,
    height: props.height,
    autoplay: props.autoplay,
    loop: props.loop,
    volume: props.volume,
    controls: props.controls,
    poster: props.poster,
    playsinline: true,
    isLive: videoType === 'hls' || videoType === 'flv',
    plugins: plugins
  }

  player = new Player(config)
}

// 销毁播放器
const destroyPlayer = () => {
  if (player) {
    player.destroy()
    player = null
  }
}

// 监听src变化
watch(
  () => props.src,
  async (newSrc) => {
    if (newSrc) {
      destroyPlayer()
      await nextTick()
      initPlayer()
    } else {
      destroyPlayer()
    }
  }
)

onMounted(() => {
  if (props.src) {
    initPlayer()
  }
})

onBeforeUnmount(() => {
  destroyPlayer()
})

// 暴露播放器实例
defineExpose({
  player: () => player
})
</script>

<style scoped>
.xg-player-container {
  width: 100%;
  height: 100%;
}

:deep(.xgplayer) {
  width: 100% !important;
  height: 100% !important;
}
</style>
