<template>
  <div :id="playerId" class="xg-player-container"></div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import Player from 'xgplayer'
import HlsPlugin from 'xgplayer-hls'
import FlvPlugin from 'xgplayer-flv'
import 'xgplayer/dist/index.min.css'

defineOptions({ name: 'SimplePlayer' })

interface Props {
  src: string
  controls?: boolean
  autoplay?: boolean
  loop?: boolean
  volume?: number
  poster?: string
  width?: string | number
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  controls: true,
  autoplay: false,
  loop: false,
  volume: 0.6,
  poster: '',
  width: '100%',
  height: '100%'
})

const playerId = `xgplayer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
let player: any = null

const initPlayer = () => {
  if (!props.src) return

  const plugins = []
  if (props.src.includes('.m3u8')) plugins.push(HlsPlugin)
  if (props.src.includes('.flv')) plugins.push(FlvPlugin)

  player = new Player({
    id: playerId,
    url: props.src,
    width: props.width,
    height: props.height,
    autoplay: props.autoplay,
    loop: props.loop,
    volume: props.volume,
    controls: props.controls,
    poster: props.poster,
    playsinline: true,
    plugins: plugins
  })

  player.on('error', (error: any) => {
    if (!error.message?.includes('AbortError')) {
      console.error('播放器错误:', error)
    }
  })
}

const destroyPlayer = () => {
  if (player) {
    try {
      player.destroy()
    } catch {}
    player = null
  }
}

watch(
  () => props.src,
  async () => {
    destroyPlayer()
    await nextTick()
    initPlayer()
  }
)

onMounted(() => {
  initPlayer()
})

onBeforeUnmount(() => {
  destroyPlayer()
})

defineExpose({
  player: () => player
})
</script>

<style scoped>
.xg-player-container {
  width: 100%;
  height: 100%;
}

:deep(.xgplayer) {
  width: 100% !important;
  height: 100% !important;
}
</style>
